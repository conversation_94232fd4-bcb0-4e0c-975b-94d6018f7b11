import React from 'react';
import { <PERSON>ton, Card, Typography, Space, Input, message } from 'antd';
import Icon from "@ant-design/icons";
import eyeoffSvg from "@/modules-smb/Wireless/assets/Details/eye_off.svg?react";
import { useTranslation } from 'react-i18next';
import { compactDate } from '@/modules-smb/helpers/dateFormatting';
import { useGetDevice } from '@/modules-smb/Wireless/hooks/Network/Devices';
import { useGetTag } from '@/modules-smb/Wireless/hooks/Network/Inventory';
import { useNavigate } from 'react-router-dom';

const { Text } = Typography;

type Props = { serialNumber: string };

const DeviceDetails: React.FC<Props> = ({ serialNumber }) => {
  const { t } = useTranslation();
  const getDevice = useGetDevice({
    serialNumber,
    disableToast: true,
    onError: () => { }
  });
  const getTag = useGetTag({ serialNumber });
  const navigate = useNavigate();


  const [isShowingPassword, setIsShowingPassword] = React.useState(false);
  const togglePassword = () => setIsShowingPassword(!isShowingPassword);

  const password =
    getDevice.data?.devicePassword && getDevice.data?.devicePassword !== ''
      ? getDevice.data.devicePassword
      : 'adminwifi';

  const getPassword = () => (isShowingPassword ? password : '••••••••••••');

  const handleCopy = async () => {
    try {
      await navigator.clipboard.writeText(password);
      message.success(t('Successfully Copied'));
    } catch {
      message.error(t('common.copyFailed') || 'Copy failed');
    }
  };

  const handleClick = (path: string) => () => navigate(`/wireless/${path}`);

  return (
    <div>
      <div className="password-section" style={{ display: 'flex', alignItems: 'center', marginBottom: 22, marginTop: -13, marginLeft: -24 }}>
        <div style={{ display: 'flex', alignItems: 'center', marginRight: 20, marginLeft: 26, marginTop: 20, fontWeight: 400, fontSize: 14, color: '#212519' }}>
          <Text>{t('common.password')}</Text>
          <span style={{ color: '#F53F3F', fontSize: 18, marginLeft: 4 }}>*</span>
        </div>
        <div style={{ display: 'flex', alignItems: 'center', gap: 14 }}>
          <Input.Password
            value={password}
            readOnly
            visibilityToggle
            iconRender={visible => (
              <Icon component={eyeoffSvg} style={{ fontSize: 16 }} />
            )}
            style={{
              marginTop: 20,
              width: 280,
              height: 36,
              borderRadius: '2px',
              border: '1px solid #B2B2B2'
            }}
          />
          <Button
            onClick={handleCopy}
            type="primary"
            style={{ background: '#14C9BB', borderRadius: 2, width: 100, height: 36, fontSize: 15, marginTop: 20 }}
          >
            {t('common.copy')}
          </Button>
        </div>
      </div>

      <div className="device-info" style={{ display: 'grid', gap: 26, gridTemplateColumns: 'repeat(3, 1fr)', marginLeft:-25 }}>
        <div style={{ background: '#F8FAFB', borderRadius: '0px', lineHeight: '48px', padding: '0 16px', marginLeft: 30 }}>
          <Text style={{ marginRight: 60, fontWeight: 400, fontSize: 14 }}>MAC</Text>
          <Text style={{ fontWeight: 600, fontSize: 14 }}>{getDevice.data?.macAddress || '-'}</Text>
        </div>

        <div style={{ background: '#F8FAFB', borderRadius: '0px', lineHeight: '48px', padding: '0 16px' }}>
          <Text style={{ marginRight: 50, fontWeight: 400, fontSize: 14 }}>{t('common.manufacturer')}</Text>
          <Text style={{ fontWeight: 600, fontSize: 14 }}>fs.com</Text>
        </div>

        <div style={{ background: '#F8FAFB', borderRadius: '0px', lineHeight: '48px', padding: '0 16px' }}>
          <Text style={{ marginRight: 135, fontWeight: 400, fontSize: 14 }}>{t('common.type')}</Text>
          <Text style={{ fontWeight: 600, fontSize: 14, marginLeft:-66 }}>{getDevice.data?.deviceType || '-'}</Text>
        </div>

        <div style={{ borderRadius: '0px', lineHeight: '48px', padding: '0 16px', marginLeft: 30 }}>
          <Text style={{ marginRight: 44, fontWeight: 400, fontSize: 14 }}>{t('common.created')}</Text>
          <Text style={{ fontWeight: 600, fontSize: 14 }}>{getDevice.data?.createdTimestamp ? compactDate(getDevice.data.createdTimestamp) : '-'}</Text>
        </div>

        <div style={{  borderRadius: '0px', lineHeight: '48px', padding: '0 16px' }}>
          <Text style={{ marginRight: 80, fontWeight: 400, fontSize: 14 }}>{t('common.modified')}</Text>
          <Text style={{ fontWeight: 600, fontSize: 14 }}>{getDevice.data?.modified ? compactDate(getDevice.data.modified) : '-'}</Text>
        </div>

        <div style={{ borderRadius: '0px', lineHeight: '48px', padding: '0 16px' }}>
          <Text style={{ marginRight: 75, fontWeight: 400, fontSize: 14 }}>{t('Site')}</Text>
          <Text style={{ fontWeight: 600, fontSize: 14 }}>
            {getTag.data?.extendedInfo?.venue?.name ? (
              <Text style={{ cursor: 'pointer', color: '#14C9BB', textDecoration: "underline", textDecorationColor: "#14C9BB", }} onClick={handleClick(`manage/Monitor#${getTag.data?.venue}`)}>
                {getTag.data.extendedInfo.venue.name}
              </Text>
            ) : 'Default'}
          </Text>
        </div>
      </div>
    </div>
  );
};

export default React.memo(DeviceDetails);
