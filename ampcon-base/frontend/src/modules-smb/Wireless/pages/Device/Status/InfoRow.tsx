import React from "react";
import { Typography, Tooltip } from "antd";

const { Text } = Typography;

interface InfoRowProps {
  label: string;
  value?: React.ReactNode;
  width?: number; 
}

const InfoRow: React.FC<InfoRowProps> = ({ label, value, width = 315 }) => {
  const stringValue =
    typeof value === "string" || typeof value === "number" ? String(value) : "";

  return (
    <div
      style={{
        width,
        display: "flex",
        alignItems: "center",
        gap: 10,
      }}
    >
      <Text
        type="secondary"
        style={{
          fontWeight: 400,
          fontSize: 14,
          color: "#929A9E",
          minWidth: 120, 
        }}
      >
        {label}
      </Text>

      <Tooltip title={stringValue || value || "----"} placement="top">
        <Text
          ellipsis
          style={{
            fontWeight: 600,
            fontSize: 14,
            color: "#212519",
            flex: 1,
          }}
        >
          {value || "----"}
        </Text>
      </Tooltip>
    </div>
  );
};

export default InfoRow;
