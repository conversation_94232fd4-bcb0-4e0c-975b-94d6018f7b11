import React, { useState, useRef } from 'react';
import { DatePicker, Button, Tooltip, Space } from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import { useTranslation } from 'react-i18next';
import dayjs, { Dayjs } from 'dayjs';

const { RangePicker } = DatePicker;

const customFooterStyle = `
.ant-picker-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 4px 12px; 
  width: 100%;
  height: auto; 
}
.ant-picker-footer-extra {
  border: none !important;
}
.ant-picker-now-btn {
  padding: 0;
  height: auto;
}
.ant-picker-time-panel-btn {
  background: transparent;
  border: none;
  box-shadow: none;
}
.ant-picker-time-panel-btn:hover {
  background: rgba(0, 0, 0, 0.04);
}
`;

interface CustomRangePickerProps {
  value: [Dayjs, Dayjs] | null;
  onChange: (dates: [Dayjs, Dayjs] | null) => void;
  width?: number;
  tooltipText?: string;
}

const CustomRangePicker: React.FC<CustomRangePickerProps> = ({
  value,
  onChange,
  width = 380,
  tooltipText = '',
}) => {
  const { t } = useTranslation();
  const [pendingTime, setPendingTime] = useState<[Dayjs, Dayjs] | null>(value);
  const [isPanelOpen, setIsPanelOpen] = useState(false);
  const [activeInputIndex, setActiveInputIndex] = useState<number | null>(null);
  const rangePickerRef = useRef<any>(null);

  const handleRangeChange = (dates: [Dayjs, Dayjs] | null) => {
    setPendingTime(dates);
    if (dates) {
      setTimeout(() => {
        setIsPanelOpen(false);
      }, 0);
    } else {
      setPendingTime(null);
      onChange(null)
    }
  };

  const handleOpenChange = (open: boolean) => {
    setIsPanelOpen(open);
    if (open && pendingTime === null) {
      setPendingTime(value);
    }
  };

  const handleRangeFocus = (e: React.FocusEvent<HTMLInputElement>) => {
    const index = e.target.placeholder === t('common.start_time') ? 0 : 1;
    setActiveInputIndex(index);
    if (!isPanelOpen) {
      setIsPanelOpen(true);
    }
  };

  const handleNow = () => {
    if (activeInputIndex === null) return;
    const now = dayjs();
    const newTime = pendingTime
      ? [...pendingTime] : ([null, null] as [Dayjs, Dayjs]);
    newTime[activeInputIndex] = now;
    setPendingTime(newTime);
  };

  const handleCalendarChange = (dates: [Dayjs, Dayjs] | null) => {
    setPendingTime(dates);
    if (dates) {
      setTimeout(() => {
        setIsPanelOpen(false);
      }, 0);
    }
  };

  // 4. 唯一确认入口：仅点击 Search 时通知父组件，提交最终选择
  const handleSearch = () => {
    onChange(pendingTime);
    setIsPanelOpen(false);
  };

  return (
    <>
      <style>{customFooterStyle}</style>
      <Space align="center" size={16}>
        <Tooltip title={tooltipText || t('controller.crud.choose_time')}>
          <RangePicker
            ref={rangePickerRef}
            value={pendingTime} // 绑定内部临时状态，而非父组件 value
            onChange={handleRangeChange}
            onCalendarChange={handleCalendarChange}
            showTime={{ format: 'HH:mm:ss' }}
            format="YYYY-MM-DD HH:mm:ss"
            placeholder={[t('common.start_time'), t('common.end_time')]}
            style={{ width, height: '32px' }}
            onFocus={handleRangeFocus}
            onOpenChange={handleOpenChange}
            open={isPanelOpen}
            renderExtraFooter={() => (
              <div className="ant-picker-footer">
                <Button
                  type="text"
                  className="ant-picker-now-btn"
                  onClick={handleNow}
                >
                  Now
                </Button>
              </div>
            )}
          />
        </Tooltip>
        <Button
          onClick={handleSearch}
          type="primary"
          style={{ width: '100px' }}
        >
          {t('common.search')}
        </Button>
      </Space>
    </>
  );
};

export default CustomRangePicker;