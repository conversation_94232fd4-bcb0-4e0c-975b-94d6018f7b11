services:
  ######################################################### server-ampcon start #########################################################
  nginx-service:
    image: ampcon-nginx:${REACT_APP_VERSION:-latest}
    build:
      context: .
      dockerfile: docker-dependencies/dockerfile/Dockerfile-NGINX
    depends_on:
      flask-main-service:
        condition: service_healthy
      owrrm:
        condition: service_healthy
    container_name: nginx-service
    restart: always
    tty: true
    environment:
      TZ: "UTC"
    logging:
      driver: syslog
      options:
        syslog-format: "rfc5424"
        syslog-address: "tcp://localhost:8514"
        tag: "nginx-service"
    volumes:
      - /etc/localtime:/etc/localtime:ro
      - ./data/nginx/sites-enabled:/etc/nginx/sites-enabled
      - ./data/nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./ampcon_data/nginx/logs:/var/log/nginx
      - ./data/dist:/etc/nginx/dist
    working_dir: /etc/nginx
    privileged: true
    healthcheck:
      test: ["CMD-SHELL", "netstat -ltn | grep -E ':80 |:443 '"]
      interval: 20s
      timeout: 15s
      retries: 10
    ports:
      - "80:80"
      - "443:443"
    networks:
      - custom_net

  flask-main-service:
    image: ampcon-main:${REACT_APP_VERSION:-latest}
    build:
      context: .
      dockerfile: docker-dependencies/dockerfile/Dockerfile-MAIN
    container_name: flask-main-service
    privileged: true
    logging:
      driver: syslog
      options:
        syslog-format: "rfc5424"
        syslog-address: "tcp://localhost:8514"
        tag: "flask-main-service"
    depends_on:
      ssh-service:
        condition: service_healthy
      celery-worker-service:
        condition: service_healthy
      celery-beat-service:
        condition: service_healthy
      redis-service:
        condition: service_healthy
      postgresql:
        condition: service_healthy
    restart: on-failure
    environment:
      TZ: "UTC"
    volumes:
      - /etc/localtime:/etc/localtime:ro
      - ./ampcon_data/openvpn_log:/var/log/openvpn
      - ./data/pre-built:/usr/share/automation/pre-built
      - ./data/agent/keys/ca.key:/etc/openvpn/server/ca.key
      - ./data/agent/keys/ca.crt:/etc/openvpn/server/ca.crt
      - ./data/agent/keys/ca.crt:/etc/openvpn/ca.crt
      - ./data/agent/keys/automation.crt:/etc/openvpn/automation.crt
      - ./data/agent/keys/automation.key:/etc/openvpn/automation.key
      - ./data/agent/keys/dh2048.pem:/etc/openvpn/dh2048.pem
      - ./data/agent/vpn_config/server.conf:/etc/openvpn/server.conf
      - ./data/agent:/usr/share/automation/server/agent
      - ./data/ansible_playbook:/usr/share/automation/server/ansible_playbook
      - ./data/celery_app/celeryconfig.py:/usr/share/automation/server/celery_app/celeryconfig.py
      - ./data/config_gen:/usr/share/automation/server/config_gen
      - ./data/img:/usr/share/automation/server/img
      - ./data/server_keys:/usr/share/automation/server/server_keys
      - ./data/vpn:/usr/share/automation/server/vpn
      - ./data/settings/inner/alembic-mysql.ini:/usr/share/automation/server/alembic-mysql.ini
      - ./data/settings/inner/alembic-postgresql.ini:/usr/share/automation/server/alembic-postgresql.ini
      - ./data/settings/inner/automation.ini:/usr/share/automation/server/automation.ini
      - ./data/settings/inner/upgrade_automation.ini:/usr/share/automation/server/upgrade_automation.ini
      - ./data/settings/inner/default_imgs_for_campus.json:/usr/share/automation/server/default_imgs.json
      - ./data/settings/inner/model_platform_mapping.json:/usr/share/automation/server/model_platform_mapping.json
      - ./data/settings/inner/request_level_role.json:/usr/share/automation/server/request_level_role.json
      - ./data/settings/outer/server.cnf:/usr/share/automation/server.cnf
      - ./data/rsyslog_conf/rsyslog.d:/etc/rsyslog.d
      - ./.env:/usr/share/automation/server/.env
      - ./data/settings/inner/ampcon-private.key:/usr/share/automation/server/ampcon-private.key
      - ./data/settings/inner/ampcon-public.key:/usr/share/automation/server/ampcon-public.key
      - ./data/settings/inner/pica-public.key:/usr/share/automation/server/pica-public.key
      - ./data/monitor/rules:/usr/share/automation/server/monitor/rules:rw
      - ./data/monitor/email_template:/usr/share/automation/server/monitor/email_template:rw
      - ./data/monitor/roce:/usr/share/automation/server/monitor/roce:rw
      - ./data/monitor/prometheus_default_values.yml:/usr/share/automation/server/monitor/prometheus_default_values.yml:rw
      - ./data/settings/inner/mac_address:/usr/share/automation/server/mac_address:ro
      - ./data/ansible_device_ssh_key:/usr/share/automation/server/ansible_device_ssh_key
      - ./data/license:/usr/share/automation/server/license
      - ./data/wireless:/usr/share/automation/server/wireless
      - ./smb/postgresql/init-data:/usr/share/automation/server/postgresql/init-data
    working_dir: /usr/share/automation/server
    healthcheck:
      test:
        [
          "CMD",
          "python",
          "-c",
          "import requests; response = requests.post('http://localhost:443/check_status')",
        ]
      interval: 15s
      timeout: 15s
      start_period: 20s
      retries: 20
    command: sh start.sh
    networks:
      - custom_net

  celery-worker-service:
    image: ampcon-main:${REACT_APP_VERSION:-latest}
    container_name: celery-worker-service
    depends_on:
      mysql-service:
        condition: service_healthy
      rabbitmq-service:
        condition: service_healthy
      openvpn-service:
        condition: service_healthy
    restart: always
    logging:
      driver: syslog
      options:
        syslog-format: "rfc5424"
        syslog-address: "tcp://localhost:8514"
        tag: "celery-worker-service"
    privileged: true
    environment:
      TZ: "UTC"
    volumes:
      - /etc/localtime:/etc/localtime:ro
      - ./ampcon_data/openvpn_log:/var/log/openvpn
      - ./data/agent/keys/ca.key:/etc/openvpn/server/ca.key
      - ./data/agent/keys/ca.crt:/etc/openvpn/server/ca.crt
      - ./data/agent/keys/ca.crt:/etc/openvpn/ca.crt
      - ./data/agent/keys/automation.crt:/etc/openvpn/automation.crt
      - ./data/agent/keys/automation.key:/etc/openvpn/automation.key
      - ./data/agent/keys/dh2048.pem:/etc/openvpn/dh2048.pem
      - ./data/agent/vpn_config/server.conf:/etc/openvpn/server.conf
      - ./data/agent:/usr/share/automation/server/agent
      - ./data/ansible_playbook:/usr/share/automation/server/ansible_playbook
      - ./data/celery_app/celeryconfig.py:/usr/share/automation/server/celery_app/celeryconfig.py
      - ./data/config_gen:/usr/share/automation/server/config_gen
      - ./data/img:/usr/share/automation/server/img
      - ./data/server_keys:/usr/share/automation/server/server_keys
      - ./data/vpn:/usr/share/automation/server/vpn
      - ./data/settings/inner/alembic-mysql.ini:/usr/share/automation/server/alembic-mysql.ini
      - ./data/settings/inner/alembic-postgresql.ini:/usr/share/automation/server/alembic-postgresql.ini
      - ./data/settings/inner/automation.ini:/usr/share/automation/server/automation.ini
      - ./.env:/usr/share/automation/server/.env
      - ./data/celery_app/celery_worker_health_check.sh:/usr/local/bin/celery_worker_health_check.sh
      - ./data/settings/inner/ampcon-private.key:/usr/share/automation/server/ampcon-private.key
      - ./data/settings/inner/ampcon-public.key:/usr/share/automation/server/ampcon-public.key
      - ./data/settings/inner/pica-public.key:/usr/share/automation/server/pica-public.key
      - ./data/settings/inner/mac_address:/usr/share/automation/server/mac_address:ro
      - ./data/ansible_device_ssh_key:/usr/share/automation/server/ansible_device_ssh_key
      - ./data/monitor/node_exporter:/usr/share/automation/server/monitor/node_exporter:rw
      - ./data/monitor/settings:/usr/share/automation/server/monitor/settings
      - ./data/license:/usr/share/automation/server/license
    working_dir: /usr/share/automation/server
    healthcheck:
      test: ["CMD", "/usr/local/bin/celery_worker_health_check.sh"]
      interval: 5s
      timeout: 10s
      retries: 20
    command:
      - sh
      - -c
      - "ip route add ********/20 via $$(getent hosts openvpn-service | awk '{ print $$1 }') && python -O /usr/local/bin/celery -A celery_app.my_celery_app worker -Q pica8_beat_task,pica8_normal_task,pica8_deploy_upgrade_task,snmp_trap_alarm -l INFO"
    networks:
      - custom_net

  celery-worker-config-distribute-service:
    image: ampcon-main:${REACT_APP_VERSION:-latest}
    container_name: celery-worker-config-distribute-service
    depends_on:
      mysql-service:
        condition: service_healthy
      rabbitmq-service:
        condition: service_healthy
      openvpn-service:
        condition: service_healthy
    restart: always
    logging:
      driver: syslog
      options:
        syslog-format: "rfc5424"
        syslog-address: "tcp://localhost:8514"
        tag: "celery-worker-config-distribute-service"
    privileged: true
    environment:
      TZ: "UTC"
    volumes:
      - /etc/localtime:/etc/localtime:ro
      - ./ampcon_data/openvpn_log:/var/log/openvpn
      - ./data/agent/keys/ca.key:/etc/openvpn/server/ca.key
      - ./data/agent/keys/ca.crt:/etc/openvpn/server/ca.crt
      - ./data/agent/keys/ca.crt:/etc/openvpn/ca.crt
      - ./data/agent/keys/automation.crt:/etc/openvpn/automation.crt
      - ./data/agent/keys/automation.key:/etc/openvpn/automation.key
      - ./data/agent/keys/dh2048.pem:/etc/openvpn/dh2048.pem
      - ./data/agent/vpn_config/server.conf:/etc/openvpn/server.conf
      - ./data/agent:/usr/share/automation/server/agent
      - ./data/ansible_playbook:/usr/share/automation/server/ansible_playbook
      - ./data/celery_app/celeryconfig.py:/usr/share/automation/server/celery_app/celeryconfig.py
      - ./data/config_gen:/usr/share/automation/server/config_gen
      - ./data/img:/usr/share/automation/server/img
      - ./data/server_keys:/usr/share/automation/server/server_keys
      - ./data/vpn:/usr/share/automation/server/vpn
      - ./data/settings/inner/alembic-mysql.ini:/usr/share/automation/server/alembic-mysql.ini
      - ./data/settings/inner/alembic-postgresql.ini:/usr/share/automation/server/alembic-postgresql.ini
      - ./data/settings/inner/automation.ini:/usr/share/automation/server/automation.ini
      - ./.env:/usr/share/automation/server/.env
      - ./data/celery_app/celery_worker_health_check.sh:/usr/local/bin/celery_worker_health_check.sh
      - ./data/settings/inner/ampcon-private.key:/usr/share/automation/server/ampcon-private.key
      - ./data/settings/inner/ampcon-public.key:/usr/share/automation/server/ampcon-public.key
      - ./data/settings/inner/pica-public.key:/usr/share/automation/server/pica-public.key
      - ./data/settings/inner/mac_address:/usr/share/automation/server/mac_address:ro
      - ./data/ansible_device_ssh_key:/usr/share/automation/server/ansible_device_ssh_key
      - ./data/monitor/node_exporter:/usr/share/automation/server/monitor/node_exporter:rw
      - ./data/monitor/settings:/usr/share/automation/server/monitor/settings
      - ./data/license:/usr/share/automation/server/license
    working_dir: /usr/share/automation/server
    healthcheck:
      test: ["CMD", "/usr/local/bin/celery_worker_health_check.sh"]
      interval: 15s
      timeout: 10s
      retries: 20
    command:
      - sh
      - -c
      - "ip route add ********/20 via $$(getent hosts openvpn-service | awk '{ print $$1 }') && python -O /usr/local/bin/celery -A celery_app.my_celery_app worker -Q config_distribution_task -l INFO"
    networks:
      - custom_net

  celery-beat-service:
    image: ampcon-main:${REACT_APP_VERSION:-latest}
    container_name: celery-beat-service
    depends_on:
      mysql-service:
        condition: service_healthy
      rabbitmq-service:
        condition: service_healthy
    restart: on-failure
    logging:
      driver: syslog
      options:
        syslog-format: "rfc5424"
        syslog-address: "tcp://localhost:8514"
        tag: "celery-beat-service"
    environment:
      TZ: "UTC"
    volumes:
      - /etc/localtime:/etc/localtime:ro
      - ./ampcon_data/openvpn_log:/var/log/openvpn
      - ./data/agent/keys/ca.key:/etc/openvpn/server/ca.key
      - ./data/agent/keys/ca.crt:/etc/openvpn/server/ca.crt
      - ./data/agent/keys/ca.crt:/etc/openvpn/ca.crt
      - ./data/agent/keys/automation.crt:/etc/openvpn/automation.crt
      - ./data/agent/keys/automation.key:/etc/openvpn/automation.key
      - ./data/agent/keys/dh2048.pem:/etc/openvpn/dh2048.pem
      - ./data/agent/vpn_config/server.conf:/etc/openvpn/server.conf
      - ./data/ansible_playbook:/usr/share/automation/server/ansible_playbook
      - ./data/celery_app/celeryconfig.py:/usr/share/automation/server/celery_app/celeryconfig.py
      - ./data/config_gen:/usr/share/automation/server/config_gen
      - ./data/img:/usr/share/automation/server/img
      - ./data/server_keys:/usr/share/automation/server/server_keys
      - ./data/vpn:/usr/share/automation/server/vpn
      - ./data/settings/inner/alembic-mysql.ini:/usr/share/automation/server/alembic-mysql.ini
      - ./data/settings/inner/alembic-postgresql.ini:/usr/share/automation/server/alembic-postgresql.ini
      - ./data/settings/inner/automation.ini:/usr/share/automation/server/automation.ini
      - ./.env:/usr/share/automation/server/.env
      - ./data/settings/inner/ampcon-private.key:/usr/share/automation/server/ampcon-private.key
      - ./data/settings/inner/ampcon-public.key:/usr/share/automation/server/ampcon-public.key
      - ./data/settings/inner/pica-public.key:/usr/share/automation/server/pica-public.key
      - ./data/settings/inner/mac_address:/usr/share/automation/server/mac_address:ro
      - ./data/ansible_device_ssh_key:/usr/share/automation/server/ansible_device_ssh_key
    working_dir: /usr/share/automation/server
    command: sh -c "python -O /usr/local/bin/celery -A celery_app.my_celery_app beat -S celery_app.utils:DatabaseScheduler -l INFO"
    healthcheck:
      test:
        [
          "CMD-SHELL",
          "if [ $(ps -ef | grep '[c]elery' | wc -l) -gt 1 ]; then exit 0; else exit 1; fi",
        ]
      interval: 15s
      timeout: 15s
      retries: 20
    networks:
      - custom_net

  ssh-service:
    image: ampcon-ssh:${REACT_APP_VERSION:-latest}
    build:
      context: .
      dockerfile: docker-dependencies/dockerfile/Dockerfile-SSH
    depends_on:
      mysql-service:
        condition: service_healthy
      rabbitmq-service:
        condition: service_healthy
      openvpn-service:
        condition: service_healthy
    container_name: ssh-service
    privileged: true
    logging:
      driver: syslog
      options:
        syslog-format: "rfc5424"
        syslog-address: "tcp://localhost:8514"
        tag: "ssh-service"
    restart: on-failure
    environment:
      TZ: "UTC"
    volumes:
      - /etc/localtime:/etc/localtime:ro
      - ./data/settings/inner/automation.ini:/usr/share/automation/server/automation.ini
      - ./data/onie_install:/usr/share/automation/server/onie_install
      - ./data/agent/auto-deploy.py:/usr/share/automation/server/agent/auto-deploy.py
      - ./data/config_gen:/usr/share/automation/server/config_gen
      - ./data/img:/usr/share/automation/server/img
      - ./data/settings/inner/model_platform_mapping.json:/usr/share/automation/server/model_platform_mapping.json
      - ./.env:/usr/share/automation/server/.env
    working_dir: /usr/share/automation/server
    command: sh -c "ip route add ********/20 via $$(getent hosts openvpn-service | awk '{ print $$1 }') && python collect/ssh_service.pyc"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80"]
      interval: 15s
      timeout: 10s
      retries: 20
    networks:
      - custom_net

  openvpn-service:
    image: ampcon-openvpn:${REACT_APP_VERSION:-latest}
    build:
      context: .
      dockerfile: docker-dependencies/dockerfile/Dockerfile-OPENVPN
    container_name: openvpn-service
    depends_on:
      rsyslog-service:
        condition: service_healthy
    restart: always
    tty: true
    environment:
      TZ: "UTC"
    volumes:
      - /etc/localtime:/etc/localtime:ro
      - ./data/openvpn:/etc/openvpn
      - ./ampcon_data/openvpn_log:/var/log/openvpn
    command: sh -c "sh add_iptables.sh >> /var/log/openvpn/iptables.log 2>&1 & exec openvpn --config /etc/openvpn/server.conf"
    working_dir: /etc/openvpn
    privileged: true
    logging:
      driver: syslog
      options:
        syslog-format: "rfc5424"
        syslog-address: "tcp://localhost:8514"
        tag: "openvpn-service"
    healthcheck:
      test: ["CMD-SHELL", "ps -ef | grep '[o]penvpn'"]
      interval: 15s
      timeout: 15s
      retries: 5
    ports:
      - "80:80/udp"
    networks:
      - custom_net

  mysql-service:
    image: ampcon-db:${REACT_APP_VERSION:-latest}
    build:
      context: .
      dockerfile: docker-dependencies/dockerfile/Dockerfile-DB
    container_name: mysql-service
    depends_on:
      rsyslog-service:
        condition: service_healthy
    restart: unless-stopped
    logging:
      driver: syslog
      options:
        syslog-format: "rfc5424"
        syslog-address: "tcp://localhost:8514"
        tag: "mysql-service"
    environment:
      TZ: "UTC"
      MYSQL_ROOT_PASSWORD: root
    healthcheck:
      #      test: out=$$(mysqladmin ping -h localhost -P 3306 -u root --password=root 2>&1); echo $$out | grep 'mysqld is alive' || { echo $$out; exit 1; }
      test:
        [
          "CMD-SHELL",
          "mysqladmin ping -h localhost -P 3306 -u root --password=root 2>&1 | grep 'mysqld is alive' && test -f /var/lib/mysql/load_all_data_done",
        ]
      interval: 25s
      timeout: 15s
      retries: 50
    volumes:
      - /etc/localtime:/etc/localtime:ro
      - ./ampcon_data/mysql:/var/lib/mysql
      - ./data/init_sql:/docker-entrypoint-initdb.d
      - ./data/custom_sql:/opt/sql
      - ./data/settings/outer/server.cnf:/etc/mysql/conf.d/my.cnf
      - ./data/mysql/restart_init:/docker-entrypoint-every-time
      - ./data/mysql/start_sh/docker-entrypoint.sh:/usr/local/bin/docker-entrypoint.sh
    ports:
      - "13306:3306"
    networks:
      - custom_net

  rabbitmq-service:
    image: rabbitmq:${REACT_APP_VERSION:-latest}
    container_name: rabbitmq-service
    restart: unless-stopped
    depends_on:
      rsyslog-service:
        condition: service_healthy
    logging:
      driver: syslog
      options:
        syslog-format: "rfc5424"
        syslog-address: "tcp://localhost:8514"
        tag: "rabbitmq-service"
    environment:
      TZ: "UTC"
      RABBITMQ_DEFAULT_VHOST: "/"
      RABBITMQ_DEFAULT_USER: admin
      RABBITMQ_DEFAULT_PASS: admin
    healthcheck:
      test: ["CMD", "/usr/local/bin/rabbitmq_health_check.sh"]
      interval: 15s
      timeout: 15s
      retries: 30
    volumes:
      - /etc/localtime:/etc/localtime:ro
      - ./data/rabbitmq/rabbitmq_health_check.sh:/usr/local/bin/rabbitmq_health_check.sh
    command: ["rabbitmq-server"]
    networks:
      - custom_net

  rsyslog-service:
    image: ampcon-rsyslog:${REACT_APP_VERSION:-latest}
    build:
      context: .
      dockerfile: docker-dependencies/dockerfile/Dockerfile-RSYSLOG
    container_name: rsyslog-service
    privileged: true
    restart: unless-stopped
    ports:
      - "8514:514"
    working_dir: /usr/local/bin
    healthcheck:
      test: ["CMD", "/usr/local/bin/rsyslog_healthcheck.sh"]
      interval: 15s
      timeout: 15s
      retries: 5
    volumes:
      - ./data/rsyslog_conf/rsyslog_healthcheck.sh:/usr/local/bin/rsyslog_healthcheck.sh
      - ./data/rsyslog_conf/start_rsyslog.sh:/usr/local/bin/start_rsyslog.sh
      - ./data/rsyslog_conf/watch_config.sh:/usr/local/bin/watch_config.sh
      - ./data/rsyslog_conf/rsyslog.conf:/etc/rsyslog.conf
      - ./data/rsyslog_conf/rsyslog.d:/etc/rsyslog.d
      - ./data/logrotate_conf:/etc/logrotate.d
      - ./ampcon_data/rsyslog:/var/log/automation
    networks:
      - custom_net

  redis-service:
    image: ampcon-redis:${REACT_APP_VERSION:-latest}
    build:
      context: .
      dockerfile: docker-dependencies/dockerfile/Dockerfile-REDIS
    container_name: redis-service
    privileged: true
    restart: always
    logging:
      driver: syslog
      options:
        syslog-format: "rfc5424"
        syslog-address: "tcp://localhost:8514"
        tag: "redis-service"
    depends_on:
      rsyslog-service:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 15s
      timeout: 15s
      retries: 10
    volumes:
      - /etc/localtime:/etc/localtime:ro
      - ./data/redis/redis.conf:/usr/local/etc/redis/redis.conf
      - ./data/redis/redis/users.acl:/usr/local/etc/redis/users.acl
      - ./ampcon_data/redis:/data
    networks:
      - custom_net

  tftp-service:
    image: stoxygen/tftpd-hpa:${REACT_APP_VERSION:-latest}
    container_name: tftp-service
    privileged: true
    restart: unless-stopped
    ports:
      - "69:69/udp"
    healthcheck:
      test: ["CMD", "test", "-f", "/etc/default/tftpd-hpa"]
      interval: 5s
      timeout: 5s
      retries: 5
    volumes:
      - ./data/onie_install/system_ztp_start.sh:/data/system_ztp_start.sh
    networks:
      - custom_net

  ######################################################### server-ampcon end  #########################################################

  ######################################################### Prometheus monitor start  ##################################################
  prometheus:
    image: prometheus:${REACT_APP_VERSION:-latest}
    container_name: prometheus
    hostname: prometheus
    restart: always
    environment:
      TZ: "UTC"
    volumes:
      - ./ampcon_data/prometheus:/prometheus/data
      - ./data/monitor/settings/prometheus.yml:/etc/prometheus/prometheus.yml # prometheus监控配置文件
      - ./data/monitor/rules:/etc/prometheus/rules # 告警规则配置文件
    command:
      - "--config.file=/etc/prometheus/prometheus.yml"
      - "--web.enable-lifecycle" # 用于配置文件热更新，调用curl -X POST http://localhost:9090/-/reload更新配置文件
      - "--storage.tsdb.retention.time=30d" # 数据过期时间
      - "--query.lookback-delta=30s" # 查询追溯时长，最多追溯30s前的数据
    ports:
      - "9090:9090"
    networks:
      - custom_net

  alertmanager:
    image: alertmanager:${REACT_APP_VERSION:-latest}
    container_name: alertmanager
    hostname: alertmanager
    restart: always
    environment:
      TZ: "UTC"
    ports:
      - 9093:9093
    networks:
      - custom_net
    volumes:
      - ./data/monitor/settings/alertmanager.yml:/etc/alertmanager/alertmanager.yml
    command:
      - "--config.file=/etc/alertmanager/alertmanager.yml"

  gnmi-exporter:
    image: gnmi-exporter:${REACT_APP_VERSION:-latest}
    build:
      context: .
      dockerfile: docker-dependencies/dockerfile/Dockerfile-GNMI-EXPORTER
    depends_on:
      nginx-service:
        condition: service_healthy
      openvpn-service:
        condition: service_healthy
    container_name: gnmi-exporter
    privileged: true
    environment:
      TZ: "UTC"
    hostname: gnmi-exporter
    restart: always
    volumes:
      - ./data/monitor/settings/gnmi.yaml:/app/gnmi.yaml:rw
    ports:
      - "5000:5000"
      - "9999:9999"
    command:
      [
        "sh",
        "-c",
        "ip route add ********/20 via $$(getent hosts openvpn-service | awk '{ print $$1 }') && ./gnmi_exporter",
      ]
    networks:
      - custom_net
  ######################################################### Prometheus monitor end  ####################################################

  ######################################################### SMB start  ##################################################
  owsec:
    image: "harbor.ampcon.com/openwifi/ampcon-smb-owsec-dev:latest"
    container_name: ow-sec
    networks:
      custom_net:
        aliases:
          - ${INTERNAL_OWSEC_HOSTNAME}
    env_file:
      - ./smb/.env.selfsigned
      - ./smb/owsec.env
    environment:
      TZ: "UTC"
    depends_on:
      init-kafka:
        condition: service_completed_successfully
      flask-main-service:
        condition: service_healthy
      # mysql-service:
      #   condition: service_healthy
      # postgresql:
      #   condition: service_healthy
    command: ["/openwifi/owsec"]
    healthcheck:
      test:
        [
          "CMD",
          "sh",
          "-c",
          "curl -k -s -o /dev/null -w '%{http_code}' https://localhost:16001 | grep -q '400' || exit 1",
        ]
      interval: 35s
      timeout: 5s
      retries: 50
      start_period: 45s
    restart: unless-stopped
    volumes:
      - "./smb/owsec_data:${OWSEC_ROOT}"
      - "./smb/certs:/${OWSEC_ROOT}/certs"
    ports:
      - "16001:16001"
      - "16101:16101"
      - "17001:17001"

  owgw:
    image: "harbor.ampcon.com/openwifi/ampcon-smb-owgw-dev:latest"
    container_name: ow-gw
    networks:
      custom_net:
        aliases:
          - ${INTERNAL_OWGW_HOSTNAME}
    env_file:
      - ./smb/.env.selfsigned
      - ./smb/owgw.env
    environment:
      TZ: "UTC"
    depends_on:
      owsec:
        condition: service_healthy
      init-kafka:
        condition: service_completed_successfully
      # postgresql:
      #   condition: service_healthy
    command: ["/openwifi/owgw"]
    healthcheck:
      test:
        [
          "CMD",
          "sh",
          "-c",
          "curl -k -s -o /dev/null -w '%{http_code}' https://localhost:16002 | grep -q '400' || exit 1",
        ]
      interval: 30s
      timeout: 5s
      retries: 50
      start_period: 35s
    restart: unless-stopped
    volumes:
      - "./smb/owgw_data:${OWGW_ROOT}"
      - "./smb/certs:/${OWGW_ROOT}/certs"
    ports:
      - "15002:15002"
      - "16002:16002"
      - "16102:16102"
      - "16003:16003"
      - "5912:5912"
      - "5913:5913"
      - "1812:1812/udp"
      - "1813:1813/udp"
      - "3799:3799/udp"
      - "17002:17002"
    sysctls:
      - net.ipv4.tcp_keepalive_intvl=5
      - net.ipv4.tcp_keepalive_probes=2
      - net.ipv4.tcp_keepalive_time=45

  # owfms:
  #   image: "harbor.ampcon.com/openwifi/ampcon-smb-owfms-dev:latest"
  #   container_name: ow-fms
  #   networks:
  #     custom_net:
  #       aliases:
  #         - ${INTERNAL_OWFMS_HOSTNAME}
  #   env_file:
  #     - ./smb/.env.selfsigned
  #     - ./smb/owfms.env
  #   depends_on:
  #     init-kafka:
  #       condition: service_completed_successfully
  #     postgresql:
  #       condition: service_healthy
  #   command: ["./wait-for-postgres.sh", "postgresql", "/openwifi/owfms"]
  #   restart: unless-stopped
  #   volumes:
  #     - "./smb/owfms_data:${OWFMS_ROOT}"
  #     - "./smb/certs:/${OWFMS_ROOT}/certs"
  owprov:
    image: "harbor.ampcon.com/openwifi/ampcon-smb-owprov-dev:latest"
    container_name: ow-prov
    networks:
      custom_net:
        aliases:
          - ${INTERNAL_OWPROV_HOSTNAME}
    env_file:
      - ./smb/.env.selfsigned
      - ./smb/owprov.env
    environment:
      TZ: "UTC"
    depends_on:
      owgw:
        condition: service_healthy
      owanalytics:
        condition: service_healthy
      init-kafka:
        condition: service_completed_successfully
      # postgresql:
      #   condition: service_healthy
    command: ["/openwifi/owprov"]
    healthcheck:
      test:
        [
          "CMD",
          "sh",
          "-c",
          "curl -k -s -o /dev/null -w '%{http_code}' https://localhost:16005 | grep -q '400' || exit 1",
        ]
      interval: 30s
      timeout: 5s
      retries: 50
      start_period: 35s
    restart: unless-stopped
    volumes:
      - "./smb/owprov_data:${OWPROV_ROOT}"
      - "./smb/certs:/${OWPROV_ROOT}/certs"
    ports:
      - "16005:16005"
      - "16105:16105"
      - "17005:17005"

  owanalytics:
    image: "harbor.ampcon.com/openwifi/ampcon-smb-owanalytics-dev:latest"
    container_name: ow-analytics
    networks:
      custom_net:
        aliases:
          - ${INTERNAL_OWANALYTICS_HOSTNAME}
    env_file:
      - ./smb/.env.selfsigned
      - ./smb/owanalytics.env
    environment:
      TZ: "UTC"
    depends_on:
      init-kafka:
        condition: service_completed_successfully
      # postgresql:
      #   condition: service_healthy
      owgw:
        condition: service_healthy
    command: ["/openwifi/owanalytics"]
    healthcheck:
      test:
        [
          "CMD",
          "sh",
          "-c",
          "curl -k -s -o /dev/null -w '%{http_code}' https://localhost:16009 | grep -q '400' || exit 1",
        ]
      interval: 20s
      timeout: 5s
      retries: 20
      start_period: 35s
    restart: unless-stopped
    volumes:
      - "./smb/owanalytics_data:${OWANALYTICS_ROOT}"
      - "./smb/certs:/${OWANALYTICS_ROOT}/certs"
    ports:
      - "16009:16009"
      - "16109:16109"
      - "17009:17009"

  owrrm:
    image: "harbor.ampcon.com/openwifi/ampcon-smb-owrrm-dev:latest"
    container_name: ow-rrm
    networks:
      custom_net:
        aliases:
          - ${INTERNAL_OWRRM_HOSTNAME}
    env_file:
      - ./smb/owrrm.env
    environment:
      TZ: "UTC"
    depends_on:
      owprov:
        condition: service_healthy
      # mysql:
      #   condition: service_healthy
      init-kafka:
        condition: service_completed_successfully
    healthcheck:
      test:
        [
          "CMD",
          "sh",
          "-c",
          "curl -k -s -o /dev/null -w '%{http_code}' http://localhost:16789 | grep -q '200' || exit 1",
        ]
      interval: 20s
      timeout: 5s
      retries: 20
      start_period: 25s
    restart: unless-stopped
    volumes:
      - "./smb/certs:/owrrm-data/certs"
      - "./smb/owrrm_data:/owrrm-data"
      - "./smb/owrrm_data/runner.sh:/runner.sh"
      - "./smb/owrrm_data/log:/usr/src/java"
    ports:
      - "16789:16789"

  # mysql:
  #   image: "harbor.ampcon.com/openwifi/mysql:${MYSQL_TAG}"
  #   container_name: ow-mysql
  #   networks:
  #     custom_net:
  #   env_file:
  #     - ./smb/mysql.env
  #   restart: unless-stopped
  #   volumes:
  #     - "./smb/mysql_data:/var/lib/mysql"
  #   healthcheck:
  #     # owsub is the last DB created in init-db.sh
  #     test: ["CMD-SHELL", "mysqladmin ping -u root -popenwifi"]
  #     interval: 30s
  #     retries: 10
  #     start_period: 10s
  #     timeout: 10s
  #   ports:
  #     - "13306:3306"

  # owsub:
  #   image: "harbor.ampcon.com/openwifi/darius-userportal:latest"
  #   networks:
  #     custom_net:
  #       aliases:
  #         - ${INTERNAL_OWSUB_HOSTNAME}
  #   env_file:
  #     - ./smb/.env.selfsigned
  #     - ./smb/owsub.env
  #   depends_on:
  #     init-kafka:
  #       condition: service_completed_successfully
  #     postgresql:
  #       condition: service_healthy
  #   command: ["./wait-for-postgres.sh", "postgresql", "/openwifi/owsub"]
  #   restart: unless-stopped
  #   volumes:
  #     - "./smb/owsub_data:${OWSUB_ROOT}"
  #     - "./smb/certs:/${OWSUB_ROOT}/certs"

  kafka:
    image: "harbor.ampcon.com/openwifi/kafka:${KAFKA_TAG}"
    container_name: ow-kafka
    networks:
      custom_net:
    env_file:
      - ./smb/kafka.env
    environment:
      TZ: "UTC"
    restart: unless-stopped
    volumes:
      - ./smb/kafka_data:/bitnami/kafka
    ports:
      - "9094:9094"

  init-kafka:
    image: "harbor.ampcon.com/openwifi/kafka:${KAFKA_TAG}"
    container_name: ow-init-kafka
    networks:
      custom_net:
    depends_on:
      - kafka
    environment:
      TZ: "UTC"
    env_file:
      - ./smb/kafka.env
    entrypoint:
      - /bin/sh
      - -c
      - |
        echo "Sleeping to allow kafka to start up..."
        sleep 10
        echo "Creating all required Kafka topics..."
        for topic in $$TOPICS; do
          /opt/bitnami/kafka/bin/kafka-topics.sh \
          --create --if-not-exists --topic $$topic --replication-factor 1 \
          --partitions 1 --bootstrap-server kafka:9092
        done && echo "Successfully created Kafka topics, exiting." && exit 0

  postgresql:
    image: "harbor.ampcon.com/openwifi/postgres:${POSTGRESQL_TAG}"
    container_name: ow-postgresql
    networks:
      custom_net:
    command:
      - "postgres"
      - "-c"
      - "max_connections=400"
      - "-c"
      - "shared_buffers=20MB"
      - "-c"
      - "config_file=/etc/postgresql/conf/postgresql.conf"
    env_file:
      - ./smb/postgresql.env
    environment:
      TZ: "UTC"
    restart: unless-stopped
    volumes:
      - ./smb/postgresql_data:/var/lib/postgresql/data
      - ./smb/postgresql/conf:/etc/postgresql/conf
      - ./smb/postgresql/logs:/var/log/postgresql
      - ./smb/postgresql/init-db.sh:/docker-entrypoint-initdb.d/init-db.sh
    healthcheck:
      # owsub is the last DB created in init-db.sh
      test: ["CMD-SHELL", "pg_isready -U smb -d smbdb"]
      interval: 10s
      retries: 10
      start_period: 30s
      timeout: 10s
    ports:
      - "15432:5432"

  # traefik:
  #   image: "harbor.ampcon.com/openwifi/traefik:${TRAEFIK_TAG}"
  #   container_name: ow-traefik
  #   networks:
  #     custom_net:
  #   env_file:
  #     - ./smb/traefik.env
  #   depends_on:
  #     - owsec
  #     - owgw
  #     # - owfms
  #     - owprov
  #     - owanalytics
  #     # - owsub
  #     - owrrm
  #   healthcheck:
  #     test: ["CMD", "nc", "-z", "-v", "owsec", "16001"]
  #     interval: 15s
  #     timeout: 5s
  #     retries: 20
  #     start_period: 15s
  #   restart: unless-stopped
  #   volumes:
  #     - "./smb/traefik/openwifi_selfsigned.yaml:/etc/traefik/openwifi.yaml"
  #     - "./smb/certs/restapi-ca.pem:/certs/restapi-ca.pem"
  #     - "./smb/certs/restapi-cert.pem:/certs/restapi-cert.pem"
  #     - "./smb/certs/restapi-key.pem:/certs/restapi-key.pem"
  #   ports:
  #     # - "15002:15002"
  #     - "16002:16002"
  #     - "16003:16003"
  #     #      - "80:80"
  #     #      - "8080:8080"
  #     #      - "443:443"
  #     #      - "8443:8443"
  #     - "16001:16001"
  #     # - "16004:16004"
  #     - "16005:16005"
  #     - "16009:16009"
  #     # - "16006:16006"
  #     - "5912:5912"
  #     - "5913:5913"
  #     - "16789:16789"
  #     - "1812:1812/udp"
  #     - "1813:1813/udp"
  #     - "3799:3799/udp"

  ######################################################### SMB end  ####################################################
networks:
  custom_net:
    driver: bridge
  #    ipam:
  #      config:
  #        - subnet: ********/24
  # openwifi:
# volumes:
#   kafka_data:
#     driver: local
#   postgresql_data:
#     driver: local
